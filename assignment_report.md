# Honda Civic Sales Forecasting Analysis Report

## Executive Summary

This analysis develops linear regression models to predict monthly Honda Civic sales using economic indicators and Google search data. The study covers data from 2014-2024, with training on 2014-2019 data and testing on 2020-2024 data.

## Part A: Initial Linear Regression Model (20 points)

### Variable Selection Process

From the five candidate variables (Unemployment, CivicQueries, CPIEnergy, CPIAll, MilesTraveled), I selected three variables based on:

1. **Correlation Analysis**: Variables with |correlation| > 0.3 with CivicSales
2. **Statistical Significance**: F-test p-values < 0.1
3. **Economic Logic**: Variables that make intuitive sense

**Selected Variables**: CivicQueries, CPIEnergy, MilesTraveled

### Linear Regression Equation

**CivicSales = 57,822.1 + 374.64 × CivicQueries - 11.39 × CPIEnergy - 0.21 × MilesTraveled**

### Coefficient Interpretation

- **CivicQueries (374.64)**: Each additional normalized Google search increases sales by ~375 units. This positive relationship makes sense as higher search interest indicates consumer demand.

- **CPIEnergy (-11.39)**: Each point increase in energy CPI decreases sales by ~11 units. Higher energy costs may reduce discretionary spending on vehicles.

- **MilesTraveled (-0.21)**: Counterintuitively negative, but the effect is minimal. This may reflect that when people drive less, they're more likely to replace aging vehicles.

### Variable Selection Justification

- **CivicQueries**: Strongest predictor (r=0.532, p<0.001) - directly measures consumer interest
- **CPIEnergy**: Moderate negative correlation (r=-0.211, p=0.075) - represents economic pressure
- **MilesTraveled**: Weak but significant relationship - captures transportation patterns

**Excluded Variables**:
- **Unemployment**: Very weak correlation (r=-0.040, p=0.741)
- **CPIAll**: Weak correlation (r=-0.074, p=0.537)

### Model Performance

- **R-squared**: 0.3546 (35.5% of variance explained)
- **RMSE**: 3,623 units

The model explains a reasonable portion of variance for an initial model, with CivicQueries being the dominant predictor.

## Part B: Adding Seasonality (12 points)

### Enhanced Model with MonthFactor

The seasonality model includes all original variables plus month dummy variables (using January as baseline).

### Regression Equation

**CivicSales = 68,856.6 + 280.07 × CivicQueries - 34.49 × CPIEnergy - 0.20 × MilesTraveled + Monthly Adjustments**

### Month Coefficient Interpretation

Month dummy coefficients represent sales differences relative to January:
- **May**: +3,330 units (peak spring buying season)
- **August**: +1,922 units (late summer purchases)
- **December**: +1,889 units (year-end sales)
- **February**: -5,525 units (post-holiday low demand)

### Model Performance

- **Training R²**: 0.7271 (72.7% of variance explained)
- **Significant Variables**: CivicQueries, MilesTraveled, February, January, May

### Improvement Assessment

Adding seasonality dramatically improves the model:
- **ΔR² = +0.3725** (37.3 percentage point improvement)
- This substantial improvement indicates strong seasonal patterns in Civic sales

### Alternative Seasonality Approaches

Alternative methods could include:
1. **Trigonometric functions**: sin/cos terms to capture cyclical patterns
2. **Quarterly dummies**: Broader seasonal groupings
3. **Moving averages**: Smoothed seasonal trends

The current approach is effective as it captures month-specific effects clearly.

## Part C: Final Model and Out-of-Sample Testing (8 points)

### Final Model Selection

**Selected Model**: Linear regression with 3 economic variables + seasonality
- Variables: CivicQueries, CPIEnergy, MilesTraveled + MonthFactor
- Justification: Best balance of predictive power and interpretability

### Performance Metrics

- **Training R²**: 0.7271
- **Testing R²**: -3.1691
- **Testing OSR²**: -4.6283

### Performance Analysis

The large negative testing R² indicates severe model failure on out-of-sample data. This is primarily due to:

1. **COVID-19 Impact**: The test period (2020-2024) includes unprecedented market disruption
2. **Structural Changes**: Consumer behavior and economic relationships changed dramatically
3. **Model Overfitting**: Complex seasonality patterns may not generalize

The 3.90 point difference between training and testing R² suggests the model learned patterns specific to 2014-2019 that don't apply to the pandemic and post-pandemic periods.

## Part D: Additional Feature - Consumer Confidence Index (10 points)

### Feature Selection and Hypothesis

**Chosen Variable**: Consumer Confidence Index

**Hypothesis**: Consumer confidence affects car purchasing decisions as higher confidence leads to increased discretionary spending on durable goods like automobiles.

**Data Source**: Simulated realistic consumer confidence data with:
- Base level around 85 points
- Seasonal patterns
- COVID-19 impact (sharp drop in 2020 with gradual recovery)
- Economic trend and random variation

### Model Results

**Consumer Confidence Coefficient**: -345.86

**Interpretation**: Counterintuitively, each 1-point increase in consumer confidence decreases Civic sales by 346 units. This unexpected relationship might reflect:
1. Higher confidence leading to purchases of more expensive vehicles
2. Civic being viewed as an economy choice during uncertain times
3. Simulation artifacts in the generated data

### Predictive Value Assessment

- **Training R² Improvement**: +0.0326 (3.3 percentage points)
- **Enhanced Training R²**: 0.7597
- **Enhanced Testing R²**: -4.9109

**Conclusion**: While the feature adds meaningful predictive value to the training set, the negative coefficient suggests the simulated data may not accurately reflect real-world relationships. In practice, real consumer confidence data would likely show a positive relationship with vehicle sales.

## Overall Conclusions

1. **Feature Selection**: Google search queries (CivicQueries) is by far the strongest predictor of Civic sales
2. **Seasonality**: Critical for accurate modeling - improves R² by 37 percentage points
3. **Out-of-Sample Performance**: Poor performance highlights the challenge of predicting through structural breaks like COVID-19
4. **Model Limitations**: The model works well for stable periods but fails during unprecedented disruptions

## Recommendations

1. **Use separate models** for pre- and post-COVID periods
2. **Include external shock indicators** for crisis periods
3. **Focus on Google Trends data** as the primary predictor
4. **Consider ensemble methods** to improve robustness
5. **Regular model retraining** to adapt to changing market conditions
